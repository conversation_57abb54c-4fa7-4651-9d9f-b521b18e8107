TABLE_CATALOG,TABLE_SCHEMA,TABLE_NAME,COLUMN_NAME,LOGICAL_NAME,ORDINAL_POSITION,COLUMN_DEFAULT,IS_NULLABLE,DATA_TYPE,KEY_POSITION,DESCRIP<PERSON>ON
pict-connect,,album_access_authorities,album_access_authority_id,�A���o���A�N�Z�X����id,1,,NO,BIGINT UNSIGNED,1,
pict-connect,,album_access_authorities,album_photo_id,�A���o��-�ʐ^id,2,,NO,BIGINT UNSIGNED,,
pict-connect,,album_access_authorities,token,�A�N�Z�X�g�[�N��,3,,YES,varchar(255),,authorized_user_id �� sns_screen_name �̂�����������Ă��Ȃ���ΕK�{
pict-connect,,album_access_authorities,sns_screen_name,�A�g��SNS �X�N���[���l�[��(@hoge),4,,YES,varchar(255),,"token �� authorized_user_id ������������Ă��Ȃ���ΕK�{
Twitter��screen_name(@hoge)�ɂ�������̂�����"
pict-connect,,album_access_authorities,authorized_user_id,���F�ς݃��[�U�[,5,,YES,BIGINT UNSIGNED,,token �� sns_screen_name ������������Ă��Ȃ���ΕK�{
pict-connect,,album_access_authorities,is_writable,�A���o�����e�̕ύX�̉�,6,false,NO,TINYINT(1),,
pict-connect,,album_access_authorities,created_at,�쐬����,7,,YES,datetime,,
pict-connect,,album_access_authorities,updated_at,�X�V����,8,,YES,datetime,,
pict-connect,,album_access_authorities,deleted_at,�폜����,9,,YES,datetime,,
pict-connect,,album_masters,album_master_id,�A���o���}�X�^id,1,,NO,BIGINT UNSIGNED,1,
pict-connect,,album_masters,user_id,�쐬���[�U�[id,2,,NO,BIGINT UNSIGNED,,
pict-connect,,album_masters,event_id,�C�x���gid,3,,YES,BIGINT UNSIGNED,,�w�肳��Ȃ��ꍇ�͌l�̃A���o���Ƃ��Ĉ���
pict-connect,,album_masters,open_range_flag,���J�͈̓t���O,4,,NO,TINYINT(1) UNSIGNED,,"0: �p�u���b�N
1: ������J(URL���L)
2: �v���C�x�[�g(���O�C�����[�U�[�̂݉{���\)"
pict-connect,,album_masters,created_at,�쐬����,5,,YES,datetime,,
pict-connect,,album_masters,updated_at,�X�V����,6,,YES,datetime,,
pict-connect,,album_masters,deleted_at,�폜����,7,,YES,datetime,,
pict-connect,,album_photos,album_photo_id,�A���o��-�ʐ^id,1,,NO,BIGINT UNSIGNED,1,
pict-connect,,album_photos,album_master_id,�A���o���}�X�^id,2,,NO,BIGINT UNSIGNED,,
pict-connect,,album_photos,event_id,�C�x���gid,3,,YES,BIGINT UNSIGNED,,"�}�X�^�[�̏��Ɠ����i�}�X�^�o�R�ŒH���Ă��������A�C�x���g�O���b�h�V���[���o�����Ƀ����[�V������t�B���^���߂�ǂ��������Ȃ̂Œǉ��j
�Ȃ��ꍇ�͌l�̃A���o���Ƃ����Ӗ�"
pict-connect,,album_photos,photo_id,�ʐ^id,4,,NO,BIGINT UNSIGNED,,
pict-connect,,album_photos,created_at,�쐬����,5,,YES,datetime,,
pict-connect,,album_photos,updated_at,�X�V����,6,,YES,datetime,,
pict-connect,,album_photos,deleted_at,�폜����,7,,YES,datetime,,
pict-connect,,events,event_id,�C�x���gid,1,,NO,BIGINT UNSIGNED,1,
pict-connect,,events,event_name,�C�x���g��,2,,NO,varchar(255),,
pict-connect,,events,event_admin_id,�C�x���g�Ǘ���id,3,,NO,BIGINT UNSIGNED,,�����l�͍쐬���[�U�[��id
pict-connect,,events,event_detail,�C�x���g�ڍ�,4,,YES,text,,
pict-connect,,events,description,���l,5,,YES,text,,
pict-connect,,events,created_at,�쐬����,6,,YES,datetime,,
pict-connect,,events,updated_at,�X�V����,7,,YES,datetime,,
pict-connect,,events,deleted_at,�폜����,8,,YES,datetime,,
pict-connect,,event_join_tokens,event_join_token_id,�C�x���g�Q���g�[�N��id,1,,NO,BIGINT UNSIGNED,1,
pict-connect,,event_join_tokens,event_id,�C�x���gid,2,,NO,BIGINT UNSIGNED,,
pict-connect,,event_join_tokens,expired_at,�L������,3,,YES,DATETIME,,null�̏ꍇ�͖�����
pict-connect,,event_join_tokens,limit_times,�L���g�p��,4,,YES,int unisgned,,null�̏ꍇ�͖�����
pict-connect,,event_join_tokens,use_times,�g�p���ꂽ��,5,,YES,int unisgned,,limit_times���w�肳��Ă��Ȃ��ꍇ��null
pict-connect,,event_join_tokens,created_at,�쐬����,6,,YES,datetime,,
pict-connect,,event_join_tokens,updated_at,�X�V����,7,,YES,datetime,,
pict-connect,,event_join_tokens,deleted_at,�폜����,8,,YES,datetime,,
pict-connect,,event_participants,event_participants_id,�C�x���g�Q����id,1,,NO,BIGINT UNSIGNED,1,
pict-connect,,event_participants,event_id,�C�x���gid,2,,NO,BIGINT UNSIGNED,,
pict-connect,,event_participants,user_id,�Q���҃��[�U�[id,3,,NO,BIGINT UNSIGNED,,
pict-connect,,event_participants,created_at,�쐬����,4,,YES,datetime,,
pict-connect,,event_participants,updated_at,�X�V����,5,,YES,datetime,,
pict-connect,,event_participants,deleted_at,�폜����,6,,YES,datetime,,
pict-connect,,guest_logins,guest_login_id,�Q�X�g���O�C��id,1,,NO,BIGINT UNSIGNED,1,
pict-connect,,guest_logins,sns_screen_name,�A�g��SNS �X�N���[���l�[��(@hoge),2,,NO,varchar(255),,Twitter��screen_name(@hoge)�ɂ�������̂�����
pict-connect,,guest_logins,sns_type,SNS���,3,,NO,TINYINT(1),,"0: pict_connect
1: Twitter
2: Mastodon
3: Misskey"
pict-connect,,guest_logins,guest_token,�F�؃g�[�N��,4,,NO,varchar(64),,
pict-connect,,guest_logins,created_at,�쐬����,5,,YES,datetime,,
pict-connect,,guest_logins,updated_at,�X�V����,6,,YES,datetime,,
pict-connect,,guest_logins,deleted_at,�폜����,7,,YES,datetime,,
pict-connect,,migrations,id,id,1,,NO,int unsigned auto_increment,1,
pict-connect,,migrations,migration,migration,2,,NO,varchar(255),,
pict-connect,,migrations,batch,batch,3,,NO,int,,
pict-connect,,migrations,created_at,�쐬����,4,,YES,datetime,,
pict-connect,,migrations,updated_at,�X�V����,5,,YES,datetime,,
pict-connect,,migrations,deleted_at,�폜����,6,,YES,datetime,,
pict-connect,,password_resets,email,���[���A�h���X,1,,NO,varchar(255),,
pict-connect,,password_resets,token,�p�X���[�h���Z�b�g�g�[�N��,2,,NO,varchar(255),,
pict-connect,,password_resets,created_at,�쐬����,3,,YES,datetime,,
pict-connect,,password_resets,updated_at,�X�V����,4,,YES,datetime,,
pict-connect,,password_resets,deleted_at,�폜����,5,,YES,datetime,,
pict-connect,,photos,photo_id,�ʐ^id,1,,NO,bigint unsigned auto_increment,1,
pict-connect,,photos,user_id,���e���[�U�[id,2,,NO,bigint,,
pict-connect,,photos,store_path,�ʐ^�ۑ��p�X,3,,YES,text,,
pict-connect,,photos,created_at,�쐬����,4,,YES,datetime,,
pict-connect,,photos,updated_at,�X�V����,5,,YES,datetime,,
pict-connect,,photos,deleted_at,�폜����,6,,YES,datetime,,
pict-connect,,photo_comments,photo_comment_id,�ʐ^�R�����gid,1,,NO,BIGINT UNSIGNED,1,
pict-connect,,photo_comments,album_photo_id,�A���o��-�ʐ^id,2,,NO,BIGINT UNSIGNED,,
pict-connect,,photo_comments,author_user_id,���e��id,3,,NO,BIGINT UNSIGNED,,
pict-connect,,photo_comments,comment,�R�����g���e,4,,NO,TEXT,,
pict-connect,,photo_comments,created_at,�쐬����,5,,YES,datetime,,
pict-connect,,photo_comments,updated_at,�X�V����,6,,YES,datetime,,
pict-connect,,photo_comments,deleted_at,�폜����,7,,YES,datetime,,
pict-connect,,photo_reactions,photo_reaction_id,�ʐ^�R�����gid,1,,NO,BIGINT UNSIGNED,1,
pict-connect,,photo_reactions,album_photo_id,�A���o��-�ʐ^id,2,,NO,BIGINT UNSIGNED,,
pict-connect,,photo_reactions,reaction_user_id,���e��id,3,,NO,BIGINT UNSIGNED,,
pict-connect,,photo_reactions,reaction,���A�N�V�������e,4,:heart:,NO,varchar(255),,"misskey,discord
�̂悤�� 
:hoge:
�̂悤�Ȍ`���B
������:heart:�̂݁B"
pict-connect,,photo_reactions,created_at,�쐬����,5,,YES,datetime,,
pict-connect,,photo_reactions,updated_at,�X�V����,6,,YES,datetime,,
pict-connect,,photo_reactions,deleted_at,�폜����,7,,YES,datetime,,
pict-connect,,sns_id_lists,pc_user_id,pict_connect���[�U�[id,1,,NO,bigint unsigned,1,
pict-connect,,sns_id_lists,sns_id,�A�g��SNSid,2,,NO,bigint,,"Twtter:���[�U�[�����R�Ɏw��ł���screen_name�ł͂Ȃ��A��ӂɍ~���Ă���id��o�^����_�ɒ���
����SNS�ł����l�̃p�����[�^�[������΂�����g�p����B�Ȃ���΃��[�U�[id���g�p����
�K�v�ɉ�����bigint => varchar �ɕύX"
pict-connect,,sns_id_lists,sns_type,SNS���,3,,NO,tinyint,,"0: pict_connect
1: Twitter
2: Mastodon
3: Misskey"
pict-connect,,sns_id_lists,created_at,�쐬����,4,,YES,datetime,,
pict-connect,,sns_id_lists,updated_at,�X�V����,5,,YES,datetime,,
pict-connect,,sns_id_lists,deleted_at,�폜����,6,,YES,datetime,,
pict-connect,,users,user_id,���[�U�[id,1,,NO,bigint unsigned,1,
pict-connect,,users,screen_name,ScreenName,2,,YES,varchar(255),,
pict-connect,,users,view_name,�\����,3,,YES,varchar(255),,
pict-connect,,users,password,�p�X���[�h(�n�b�V�����ς�),4,,YES,varchar(255),,SNS���O�C���̏ꍇ�͔C��
pict-connect,,users,user_icon_path,���[�U�[�A�C�R���̃p�X,5,,YES,text,,�C��
pict-connect,,users,token,�F�؃g�[�N��,6,,YES,text,,��������
pict-connect,,users,token_sec,�F�؃g�[�N��(sec),7,,YES,text,,��������
pict-connect,,users,remember_token,remember�g�[�N��,8,,YES,varchar(100),,Laravel�W�� ��������
pict-connect,,users,description,���l,9,,YES,text,,
pict-connect,,users,is_from_sns,SNS���O�C���𗘗p���ēo�^�������[�U�[���H,10,0,NO,tinyint,,
pict-connect,,users,email,���[���A�h���X,11,,YES,varchar(255),,
pict-connect,,users,created_at,�쐬����,12,,YES,datetime,,
pict-connect,,users,updated_at,�X�V����,13,,YES,datetime,,
pict-connect,,users,deleted_at,�폜����,14,,YES,datetime,,
